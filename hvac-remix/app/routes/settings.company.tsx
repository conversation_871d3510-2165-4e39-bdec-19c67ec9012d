import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { getCompanySettings, updateCompanySettings } from "~/services/company-settings.server";
import { requireUserId } from "~/session.server";
import { useState } from "react";
import { CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";

export async function loader({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  const companySettings = await getCompanySettings();
  return json({ companySettings });
}

export async function action({ request }: ActionFunctionArgs) {
  await requireUserId(request);
  const formData = await request.formData();

  const name = formData.get("name") as string;
  const address = formData.get("address") as string;
  const city = formData.get("city") as string;
  const postalCode = formData.get("postalCode") as string;
  const country = formData.get("country") as string;
  const phone = formData.get("phone") as string;
  const email = formData.get("email") as string;
  const website = formData.get("website") as string;
  const taxId = formData.get("taxId") as string;
  const logoUrl = formData.get("logoUrl") as string;
  const primaryColor = formData.get("primaryColor") as string;
  const secondaryColor = formData.get("secondaryColor") as string;
  const bankName = formData.get("bankName") as string;
  const bankAccountNumber = formData.get("bankAccountNumber") as string;
  const bankSwift = formData.get("bankSwift") as string;
  const invoiceFooter = formData.get("invoiceFooter") as string;
  const reportFooter = formData.get("reportFooter") as string;
  const termsAndConditions = formData.get("termsAndConditions") as string;

  // Validate required fields
  if (!name) {
    return json(
      { errors: { name: "Company name is required" } },
      { status: 400 }
    );
  }

  // Update company settings
  await updateCompanySettings({
    name,
    address,
    city,
    postalCode,
    country,
    phone,
    email,
    website,
    taxId,
    logoUrl,
    primaryColor,
    secondaryColor,
    bankName,
    bankAccountNumber,
    bankSwift,
    invoiceFooter,
    reportFooter,
    termsAndConditions,
  });

  return json({ success: true });
}

export default function CompanySettingsPage() {
  const { companySettings } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // Show success message when form is successfully submitted
  if (actionData?.success && !showSuccessMessage) {
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
  }

  return (
    <div className="mx-auto max-w-4xl">
      <h1 className="mb-6 text-2xl font-bold">Company Settings</h1>
      <p className="mb-6 text-gray-600">
        Configure your company information for invoices, service reports, and other documents.
      </p>

      {showSuccessMessage && (
        <div className="mb-6 flex items-center rounded-md bg-green-100 p-4 text-green-800">
          <CheckIcon className="mr-2 h-5 w-5" />
          <span>Company settings updated successfully!</span>
        </div>
      )}

      <Form method="post" className="space-y-8">
        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
            <h2 className="text-lg font-medium text-gray-900">Company Information</h2>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Company Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  defaultValue={companySettings.name}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  required
                />
                {actionData?.errors?.name && (
                  <p className="mt-1 text-sm text-red-600">{actionData.errors.name}</p>
                )}
              </div>

              <div>
                <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                  Address
                </label>
                <input
                  type="text"
                  id="address"
                  name="address"
                  defaultValue={companySettings.address || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="city" className="block text-sm font-medium text-gray-700">
                  City
                </label>
                <input
                  type="text"
                  id="city"
                  name="city"
                  defaultValue={companySettings.city || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="postalCode" className="block text-sm font-medium text-gray-700">
                  Postal Code
                </label>
                <input
                  type="text"
                  id="postalCode"
                  name="postalCode"
                  defaultValue={companySettings.postalCode || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="country" className="block text-sm font-medium text-gray-700">
                  Country
                </label>
                <input
                  type="text"
                  id="country"
                  name="country"
                  defaultValue={companySettings.country || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                  Phone
                </label>
                <input
                  type="text"
                  id="phone"
                  name="phone"
                  defaultValue={companySettings.phone || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  defaultValue={companySettings.email || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="website" className="block text-sm font-medium text-gray-700">
                  Website
                </label>
                <input
                  type="text"
                  id="website"
                  name="website"
                  defaultValue={companySettings.website || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="taxId" className="block text-sm font-medium text-gray-700">
                  Tax ID / VAT Number
                </label>
                <input
                  type="text"
                  id="taxId"
                  name="taxId"
                  defaultValue={companySettings.taxId || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="logoUrl" className="block text-sm font-medium text-gray-700">
                  Logo URL
                </label>
                <input
                  type="text"
                  id="logoUrl"
                  name="logoUrl"
                  defaultValue={companySettings.logoUrl || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  placeholder="https://example.com/logo.png"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Enter a URL to your company logo. Recommended size: 300x100px.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
            <h2 className="text-lg font-medium text-gray-900">Branding</h2>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label htmlFor="primaryColor" className="block text-sm font-medium text-gray-700">
                  Primary Color
                </label>
                <div className="mt-1 flex items-center">
                  <input
                    type="color"
                    id="primaryColor"
                    name="primaryColor"
                    defaultValue={companySettings.primaryColor || "#3b82f6"}
                    className="h-10 w-10 rounded-md border border-gray-300 p-0"
                  />
                  <input
                    type="text"
                    value={companySettings.primaryColor || "#3b82f6"}
                    className="ml-2 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    readOnly
                  />
                </div>
              </div>

              <div>
                <label htmlFor="secondaryColor" className="block text-sm font-medium text-gray-700">
                  Secondary Color
                </label>
                <div className="mt-1 flex items-center">
                  <input
                    type="color"
                    id="secondaryColor"
                    name="secondaryColor"
                    defaultValue={companySettings.secondaryColor || "#10b981"}
                    className="h-10 w-10 rounded-md border border-gray-300 p-0"
                  />
                  <input
                    type="text"
                    value={companySettings.secondaryColor || "#10b981"}
                    className="ml-2 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    readOnly
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
            <h2 className="text-lg font-medium text-gray-900">Banking Information</h2>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label htmlFor="bankName" className="block text-sm font-medium text-gray-700">
                  Bank Name
                </label>
                <input
                  type="text"
                  id="bankName"
                  name="bankName"
                  defaultValue={companySettings.bankName || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="bankAccountNumber" className="block text-sm font-medium text-gray-700">
                  Bank Account Number / IBAN
                </label>
                <input
                  type="text"
                  id="bankAccountNumber"
                  name="bankAccountNumber"
                  defaultValue={companySettings.bankAccountNumber || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="bankSwift" className="block text-sm font-medium text-gray-700">
                  SWIFT / BIC Code
                </label>
                <input
                  type="text"
                  id="bankSwift"
                  name="bankSwift"
                  defaultValue={companySettings.bankSwift || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
            <h2 className="text-lg font-medium text-gray-900">Document Settings</h2>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label htmlFor="invoiceFooter" className="block text-sm font-medium text-gray-700">
                  Invoice Footer
                </label>
                <textarea
                  id="invoiceFooter"
                  name="invoiceFooter"
                  rows={3}
                  defaultValue={companySettings.invoiceFooter || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  placeholder="Thank you for your business!"
                ></textarea>
              </div>

              <div>
                <label htmlFor="reportFooter" className="block text-sm font-medium text-gray-700">
                  Service Report Footer
                </label>
                <textarea
                  id="reportFooter"
                  name="reportFooter"
                  rows={3}
                  defaultValue={companySettings.reportFooter || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  placeholder="We appreciate your trust in our services."
                ></textarea>
              </div>

              <div>
                <label htmlFor="termsAndConditions" className="block text-sm font-medium text-gray-700">
                  Terms and Conditions
                </label>
                <textarea
                  id="termsAndConditions"
                  name="termsAndConditions"
                  rows={5}
                  defaultValue={companySettings.termsAndConditions || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  placeholder="Payment due within 14 days of invoice date. Late payments subject to 2% monthly interest."
                ></textarea>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-blue-400"
          >
            {isSubmitting ? "Saving..." : "Save Settings"}
          </button>
        </div>
      </Form>
    </div>
  );
}